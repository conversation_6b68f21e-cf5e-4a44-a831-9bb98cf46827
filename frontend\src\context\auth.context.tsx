import { createContext } from 'react';

type AuthContextProviderProps = {
  children: React.ReactNode;
};

type AuthContextTypes = {
  register: (RegisterData) => Promise<void>;
  login: (LoginData) => Promise<void>;
  logout: () => void;
};

export const AuthContext = createContext(AuthContextTypes);

export function AuthContextProvider({ children }: AuthContextProviderProps) {
  return <AuthContext.Provider value={{}}>{children}</AuthContext.Provider>;
}

import 'dotenv/config';
import { connectDB } from '../configs/connectdb.config';
import { userModel } from '../models/user.models';
import { hashPassword } from '../utils/bcrypt.utils';

const createDemoUsers = async () => {
  try {
    await connectDB();
    
    // Clear existing demo users
    await userModel.deleteMany({ email: { $in: ['<EMAIL>', '<EMAIL>', '<EMAIL>'] } });
    
    const demoUsers = [
      {
        fullName: '<PERSON> Johnson',
        email: '<EMAIL>',
        password: await hashPassword('password123'),
        profilePic: '',
      },
      {
        fullName: '<PERSON>',
        email: '<EMAIL>',
        password: await hashPassword('password123'),
        profilePic: '',
      },
      {
        fullName: '<PERSON> Brown',
        email: '<EMAIL>',
        password: await hashPassword('password123'),
        profilePic: '',
      },
    ];

    const createdUsers = await userModel.insertMany(demoUsers);
    
    console.log('Demo users created successfully:');
    createdUsers.forEach(user => {
      console.log(`- ${user.fullName} (${user.email})`);
    });
    
    console.log('\nYou can now login with any of these accounts using password: password123');
    
    process.exit(0);
  } catch (error) {
    console.error('Error creating demo users:', error);
    process.exit(1);
  }
};

createDemoUsers();

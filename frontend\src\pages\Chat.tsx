import React, { useState, useEffect } from 'react';
import { SocketProvider } from '../context/socket.context';
import ChatContainer from '../components/Chat/ChatContainer';

interface User {
  _id: string;
  fullName: string;
  email: string;
  profilePic?: string;
}

const Chat: React.FC = () => {
  const [currentUser, setCurrentUser] = useState<User | null>(null);
  const [token, setToken] = useState<string>('');
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // For demo purposes, we'll create a mock user
    // In a real app, you'd get this from your auth context or API
    const mockUser: User = {
      _id: '507f1f77bcf86cd799439011', // Mock ObjectId
      fullName: '<PERSON>',
      email: '<EMAIL>',
      profilePic: ''
    };

    const mockToken = 'mock-jwt-token-here';

    setCurrentUser(mockUser);
    setToken(mockToken);
    setLoading(false);
  }, []);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-100">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600">Loading chat...</p>
        </div>
      </div>
    );
  }

  if (!currentUser || !token) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-100">
        <div className="text-center">
          <div className="w-16 h-16 bg-red-100 rounded-full mx-auto mb-4 flex items-center justify-center">
            <svg className="w-8 h-8 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
          <h2 className="text-xl font-semibold text-gray-800 mb-2">Authentication Required</h2>
          <p className="text-gray-600">Please log in to access the chat</p>
        </div>
      </div>
    );
  }

  return (
    <SocketProvider token={token} userId={currentUser._id}>
      <div className="h-screen">
        <ChatContainer currentUser={currentUser} token={token} />
      </div>
    </SocketProvider>
  );
};

export default Chat;

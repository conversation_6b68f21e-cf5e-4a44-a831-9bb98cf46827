{"name": "backend", "version": "1.0.0", "main": "dist/server.js", "scripts": {"dev": "nodemon"}, "keywords": [], "author": "", "license": "ISC", "description": "", "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/cookie-parser": "^1.4.8", "@types/cors": "^2.8.18", "@types/express": "^5.0.3", "@types/jsonwebtoken": "^9.0.9", "@types/mongoose": "^5.11.96", "@types/multer": "^1.4.12", "@types/node": "^22.15.30", "nodemon": "^3.1.10", "ts-node": "^10.9.2", "typescript": "^5.8.3"}, "dependencies": {"bcryptjs": "^3.0.2", "cloudinary": "^1.41.3", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.15.1", "multer": "^2.0.1", "multer-storage-cloudinary": "^4.0.0", "socket.io": "^4.8.1"}}